import React, { useEffect } from "react";
import { RefreshCw, Download, Trash2, FileIcon } from "lucide-react";
import { useFileOperations } from "@/hooks/useFileOperations";
import { formatFileSize, formatDate } from "@/utils/file";
import { useToast } from "@/hooks/useToast";
import { useConfirmDialog } from "@/hooks/useConfirmDialog";
import Toast from "./Toast";
import ConfirmDialog from "./ConfirmDialog";

interface UploadedFilesDisplayProps {
  refreshTrigger?: number;
}

export const UploadedFilesDisplay: React.FC<UploadedFilesDisplayProps> = ({ 
  refreshTrigger 
}) => {
  const { toast, hideToast } = useToast();
  const { dialogState, showConfirmDialog } = useConfirmDialog();
  const {
    files,
    loading,
    error,
    deletingKey,
    fetchFiles,
    deleteFile,
    getDownloadUrl,
  } = useFileOperations();

  useEffect(() => {
    fetchFiles();
  }, [refreshTrigger, fetchFiles]);

  const handleDelete = async (key: string, filename: string) => {
    showConfirmDialog(
      "Delete File",
      `Are you sure you want to delete ${filename}?`,
      async () => {
        await deleteFile(key);
      },
      {
        confirmText: "Delete",
        cancelText: "Cancel",
        isDestructive: true,
      }
    );
  };

  const handleDownload = async (key: string) => {
    const downloadUrl = await getDownloadUrl(key);
    if (downloadUrl) {
      window.open(downloadUrl, "_blank");
    }
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow">
        <div className="flex justify-between items-center p-4 border-b">
          <h2 className="text-lg font-semibold">Files Uploaded (Ready to be Analyzed)</h2>
        </div>
        <div className="flex justify-center p-8">
          <RefreshCw className="w-6 h-6 animate-spin text-gray-400" />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white rounded-lg shadow">
        <div className="flex justify-between items-center p-4 border-b">
          <h2 className="text-lg font-semibold">Files Uploaded (Ready to be Analyzed)</h2>
        </div>
        <div className="text-center p-8">
          <p className="text-red-600 mb-4">{error}</p>
          <button
            onClick={fetchFiles}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <>
      {toast.isOpen && (
        <Toast
          message={toast.message}
          type={toast.type}
          onClose={hideToast}
        />
      )}
      <ConfirmDialog {...dialogState} />
      
      <div className="bg-white rounded-lg shadow">
        <div className="flex justify-between items-center p-4 border-b">
          <h2 className="text-lg font-semibold">
            Files Uploaded (Ready to be Analyzed) ({files.length})
          </h2>
          <button
            onClick={fetchFiles}
            className="p-2 hover:bg-gray-100 rounded transition-colors"
            title="Refresh"
          >
            <RefreshCw className="w-4 h-4" />
          </button>
        </div>

        {files.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            No uploaded files available
          </div>
        ) : (
          <div className="overflow-hidden">
            <table className="w-full">
              <thead className="bg-gray-50 border-b">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    File Name
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Size
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Modified
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {files.map((file) => (
                  <tr key={file.key} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <FileIcon className="w-5 h-5 mr-2 text-gray-400" />
                        <span className="text-sm font-medium text-gray-900">
                          {file.filename}
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {formatFileSize(file.size)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {formatDate(file.last_modified)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <button
                        onClick={() => handleDownload(file.key)}
                        className="text-blue-600 hover:text-blue-900 mr-3"
                        title="Download"
                      >
                        <Download className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleDelete(file.key, file.filename)}
                        disabled={deletingKey === file.key}
                        className="text-red-600 hover:text-red-900 disabled:opacity-50"
                        title="Delete"
                      >
                        {deletingKey === file.key ? (
                          <RefreshCw className="w-4 h-4 animate-spin" />
                        ) : (
                          <Trash2 className="w-4 h-4" />
                        )}
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </>
  );
};
